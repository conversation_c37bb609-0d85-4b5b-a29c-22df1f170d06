/**
 * HTTP客户端服务
 * 封装HTTP请求，处理签名、重试、错误处理等
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { 
  BaseApiResponse, 
  DeviceConfig, 
  RequestConfig, 
  DangbeiApiError, 
  ErrorType 
} from '../types';
import { SignatureUtils, DeviceUtils } from '../utils';
import { SignatureV2Utils } from '../utils/signature-v2';

/**
 * HTTP客户端类
 * 负责处理所有HTTP请求，包括签名生成、错误处理和重试机制
 */
export class HttpClient {
  private readonly axiosInstance: AxiosInstance;
  private readonly deviceConfig: DeviceConfig;
  private readonly baseURL: string = 'https://ai-api.dangbei.net';
  private readonly debug: boolean;

  constructor(deviceConfig?: Partial<DeviceConfig>, debug: boolean = false) {
    // 初始化调试模式
    this.debug = debug;

    // 创建设备配置
    this.deviceConfig = {
      ...DeviceUtils.createDefaultDeviceConfig(),
      ...deviceConfig
    };

    // 创建axios实例
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: 30000, // 30秒超时
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // 设置请求拦截器
    this.setupRequestInterceptor();
    
    // 设置响应拦截器
    this.setupResponseInterceptor();
  }

  /**
   * 设置请求拦截器
   * 自动添加签名和标准请求头，并打印详细的请求日志
   */
  private setupRequestInterceptor(): void {
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 检测是否为v2接口
        const isV2Interface = SignatureV2Utils.isV2Interface(config.url || '');

        if (isV2Interface) {
          // v2接口使用完整的签名对象
          this.setupV2RequestHeaders(config);
        } else {
          // v1接口使用传统的签名方式
          this.setupV1RequestHeaders(config);
        }

        // 打印详细的请求日志（仅在调试模式下）
        if (this.debug) {
          this.logRequest(config);
        }

        return config;
      },
      (_error) => {
        return Promise.reject(new DangbeiApiError(
          '请求配置失败',
          ErrorType.PARAMETER_ERROR
        ));
      }
    );
  }

  /**
   * 设置v2接口请求头
   * 使用WASM返回的完整签名对象（包含nonce、sign、timestamp）
   */
  private setupV2RequestHeaders(config: AxiosRequestConfig): void {
    // 准备请求体数据
    let bodyRaw: string | undefined;
    if ((config.method || 'POST').toString().toUpperCase() === 'POST') {
      if (typeof config.data === 'string') {
        bodyRaw = config.data;
      } else if (config.data && typeof config.data === 'object') {
        try {
          bodyRaw = JSON.stringify(config.data);
          (config as any).data = bodyRaw;
        } catch {
          bodyRaw = '';
          (config as any).data = bodyRaw;
        }
      } else {
        bodyRaw = '';
        (config as any).data = bodyRaw;
      }
    }

    // 使用v2签名算法获取完整的签名结果
    const signatureResult = SignatureV2Utils.generateV2SignatureComplete(
      {
        deviceId: this.deviceConfig.deviceId,
        method: (config.method || 'POST').toString(),
        url: (config.url || '').toString(),
        ...(typeof bodyRaw === 'string' ? { bodyRaw } : {}),
      } as any,
      {
        appType: this.deviceConfig.appType,
        appVersion: this.deviceConfig.appVersion,
        clientVersion: this.deviceConfig.clientVersion,
        lang: this.deviceConfig.lang
      }
    );

    // 添加标准请求头，使用WASM返回的完整参数
    const standardHeaders = DeviceUtils.getStandardHeaders(this.deviceConfig, {
      'timestamp': signatureResult.timestamp.toString(),
      'nonce': signatureResult.nonce,
      'sign': signatureResult.sign,
      'version': 'v2'  // 标识v2接口
    });

    config.headers = {
      ...config.headers,
      ...standardHeaders
    } as any;

    if (this.debug) {
      console.log('🔐 v2接口签名结果:', {
        nonce: signatureResult.nonce,
        sign: signatureResult.sign.substring(0, 8) + '...',
        timestamp: signatureResult.timestamp,
        url: config.url
      });
    }
  }

  /**
   * 设置v1接口请求头
   * 使用传统的签名方式
   */
  private setupV1RequestHeaders(config: AxiosRequestConfig): void {
    // 生成时间戳和nonce（确保与请求体中的时间戳保持一致）
    let timestamp = SignatureUtils.getTimestamp();
    const nonce = SignatureUtils.generateNonce();

    // 如果请求体中包含时间戳，使用请求体中的时间戳的秒级版本进行签名
    if (config.data && typeof config.data === 'object' && 'timestamp' in config.data) {
      const bodyTimestamp = (config.data as any).timestamp;
      if (typeof bodyTimestamp === 'number') {
        // 将毫秒级时间戳转换为秒级时间戳用于签名
        timestamp = Math.floor(bodyTimestamp / 1000);
      }
    }

    // 生成签名（严格对齐 _app O(e,t) 规则：GET取querystring；POST取原始body字符串）
    let bodyRaw: string | undefined;
    if ((config.method || 'POST').toString().toUpperCase() === 'POST') {
      if (typeof config.data === 'string') {
        bodyRaw = config.data;
      } else if (config.data && typeof config.data === 'object') {
        try {
          bodyRaw = JSON.stringify(config.data);
          (config as any).data = bodyRaw;
        } catch {
          bodyRaw = '';
          (config as any).data = bodyRaw;
        }
      } else {
        bodyRaw = '';
        (config as any).data = bodyRaw;
      }
    }

    const signature = SignatureUtils.generateSignature(
      {
        timestamp,
        nonce,
        deviceId: this.deviceConfig.deviceId,
        data: config.data as any,
        method: (config.method || 'POST').toString(),
        url: (config.url || '').toString(),
        ...(typeof bodyRaw === 'string' ? { bodyRaw } : {}),
      } as any,
      {
        appType: this.deviceConfig.appType,
        appVersion: this.deviceConfig.appVersion,
        clientVersion: this.deviceConfig.clientVersion,
        lang: this.deviceConfig.lang
      }
    );

    // 添加标准请求头
    const standardHeaders = DeviceUtils.getStandardHeaders(this.deviceConfig, {
      'timestamp': timestamp.toString(),
      'nonce': nonce,
      'sign': signature
    });

    config.headers = {
      ...config.headers,
      ...standardHeaders
    } as any;
  }

  /**
   * 设置响应拦截器
   * 处理API响应和错误，并打印详细的响应日志
   */
  private setupResponseInterceptor(): void {
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse<BaseApiResponse>) => {
        // 打印详细的响应日志（仅在调试模式下）
        if (this.debug) {
          this.logResponse(response);
        }

        // 检查API响应状态
        if (!response.data.success) {
          throw new DangbeiApiError(
            response.data.errMessage || '未知API错误',
            ErrorType.API_ERROR,
            response.data.errCode || undefined,
            response.data.requestId
          );
        }
        return response;
      },
      (error) => {
        // 打印详细的错误日志（仅在调试模式下）
        if (this.debug) {
          console.log('\n❌ ===== HTTP响应错误 =====');
          console.log(`📍 请求URL: ${error.config?.url}`);
          console.log(`💥 错误状态: ${error.response?.status} ${error.response?.statusText}`);
          if (error.response?.data) {
            console.log('📦 错误数据:', JSON.stringify(error.response.data, null, 2));
          }
          console.log('⏰ 错误时间:', new Date().toLocaleString('zh-CN'));

          // 特殊处理签名相关错误
          const data = error.response?.data as any;
          if (data?.errCode === '5002' || data?.errCode === '4001') {
            console.log('🔍 签名相关错误检测:');
            console.log('   - 这可能是签名算法不匹配导致的');

            // 检查是否为v2接口
            const isV2 = error.config?.url?.includes('/v2/') || error.config?.url?.includes('chatApi');
            if (isV2) {
              console.log('   - 检测到v2接口签名失败');
              console.log('   - v2接口使用了未知的专有签名算法');
              console.log('   - 建议查看项目文档中的v2接口说明');
            }

            console.log('   - 程序将继续使用本地备用方案');
          }
          console.log('================================\n');
        }

        if (axios.isAxiosError(error)) {
          if (error.code === 'ECONNABORTED') {
            throw new DangbeiApiError(
              '请求超时',
              ErrorType.TIMEOUT_ERROR
            );
          }

          if (error.response) {
            // 如果响应包含API错误信息，使用它
            const data = error.response.data as any;
            if (data && typeof data === 'object' && 'errMessage' in data) {
              throw new DangbeiApiError(
                data.errMessage || '未知API错误',
                ErrorType.API_ERROR,
                data.errCode || undefined,
                data.requestId
              );
            }

            throw new DangbeiApiError(
              `HTTP错误: ${error.response.status} ${error.response.statusText}`,
              ErrorType.NETWORK_ERROR
            );
          }

          if (error.request) {
            throw new DangbeiApiError(
              '网络连接失败',
              ErrorType.NETWORK_ERROR
            );
          }
        }

        throw new DangbeiApiError(
          error.message || '未知错误',
          ErrorType.UNKNOWN_ERROR
        );
      }
    );
  }

  /**
   * 发送GET请求
   * 
   * @param url 请求URL
   * @param config 请求配置
   * @returns 响应数据
   */
  public async get<T>(
    url: string, 
    config?: RequestConfig
  ): Promise<BaseApiResponse<T>> {
    return this.request<T>('GET', url, undefined, config);
  }

  /**
   * 发送POST请求
   * 
   * @param url 请求URL
   * @param data 请求数据
   * @param config 请求配置
   * @returns 响应数据
   */
  public async post<T>(
    url: string, 
    data?: unknown, 
    config?: RequestConfig
  ): Promise<BaseApiResponse<T>> {
    return this.request<T>('POST', url, data, config);
  }

  /**
   * 发送HTTP请求（带重试机制）
   * 
   * @param method HTTP方法
   * @param url 请求URL
   * @param data 请求数据
   * @param config 请求配置
   * @returns 响应数据
   */
  private async request<T>(
    method: 'GET' | 'POST',
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<BaseApiResponse<T>> {
    const retries = config?.retries || 3;
    const retryDelay = config?.retryDelay || 1000;
    
    let lastError: Error;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const axiosConfig: AxiosRequestConfig = {
          method,
          url,
          data,
          ...(config?.timeout && { timeout: config.timeout })
        };

        const response = await this.axiosInstance.request<BaseApiResponse<T>>(axiosConfig);
        return response.data;
      } catch (error) {
        lastError = error as Error;
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === retries) {
          break;
        }

        // 如果是参数错误或API错误，不进行重试
        if (error instanceof DangbeiApiError && 
            (error.type === ErrorType.PARAMETER_ERROR || 
             error.type === ErrorType.API_ERROR)) {
          break;
        }

        // 等待后重试
        await this.delay(retryDelay * (attempt + 1));
      }
    }

    throw lastError!;
  }

  /**
   * 延迟函数
   * 
   * @param ms 延迟毫秒数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取设备配置
   * 
   * @returns 当前设备配置
   */
  public getDeviceConfig(): DeviceConfig {
    return { ...this.deviceConfig };
  }

  /**
   * 更新设备配置
   *
   * @param config 新的设备配置
   */
  public updateDeviceConfig(config: Partial<DeviceConfig>): void {
    Object.assign(this.deviceConfig, config);
  }

  /**
   * 打印详细的HTTP请求日志
   * 包括请求URL、方法、请求头和请求体内容
   *
   * @param config Axios请求配置
   */
  private logRequest(config: AxiosRequestConfig): void {
    console.log('\n🚀 ===== HTTP请求详情 =====');
    console.log(`📍 请求URL: ${config.baseURL || this.baseURL}${config.url}`);
    console.log(`🔧 请求方法: ${(config.method || 'GET').toUpperCase()}`);

    // 打印请求头信息（隐藏敏感信息）
    console.log('📋 请求头信息:');
    if (config.headers) {
      const headers = { ...config.headers } as any;
      // 隐藏敏感的签名信息，只显示前几位
      if (headers['sign']) {
        headers['sign'] = `${headers['sign'].substring(0, 8)}...`;
      }
      console.log(JSON.stringify(headers, null, 2));
    }

    // 打印请求体内容
    if (config.data) {
      console.log('📦 请求体内容:');
      console.log(JSON.stringify(config.data, null, 2));
    } else {
      console.log('📦 请求体: 无数据');
    }

    console.log('⏰ 请求时间:', new Date().toLocaleString('zh-CN'));
    console.log('================================\n');
  }

  /**
   * 打印详细的HTTP响应日志
   * 包括响应状态码、响应头和响应数据
   *
   * @param response Axios响应对象
   */
  private logResponse(response: AxiosResponse<BaseApiResponse>): void {
    console.log('\n📥 ===== HTTP响应详情 =====');
    console.log(`📍 响应URL: ${response.config.url}`);
    console.log(`✅ 响应状态码: ${response.status} ${response.statusText}`);

    // 打印响应头信息（只显示重要的头信息）
    console.log('📋 响应头信息:');
    const importantHeaders = {
      'content-type': response.headers['content-type'],
      'content-length': response.headers['content-length'],
      'server': response.headers['server'],
      'date': response.headers['date']
    };
    console.log(JSON.stringify(importantHeaders, null, 2));

    // 打印响应数据（限制长度避免日志过长）
    console.log('📦 响应数据:');
    const responseData = response.data;
    if (responseData) {
      // 如果响应数据太长，只显示前500个字符
      const dataStr = JSON.stringify(responseData, null, 2);
      if (dataStr.length > 500) {
        console.log(dataStr.substring(0, 500) + '...\n[响应数据过长，已截断显示]');
      } else {
        console.log(dataStr);
      }
    } else {
      console.log('无响应数据');
    }

    console.log('⏰ 响应时间:', new Date().toLocaleString('zh-CN'));
    console.log('================================\n');
  }
}
