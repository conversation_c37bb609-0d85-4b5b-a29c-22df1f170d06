const { DangbeiProvider } = require('./dist');

async function testSimple() {
  console.log('🧪 简单测试...');
  
  const provider = new DangbeiProvider({}, false);
  
  // 测试同步聊天
  try {
    const result = await provider.chatSync({
      conversationId: 'test-' + Date.now(),
      question: '你好'
    });
    
    console.log('✅ 同步聊天成功:', JSON.stringify(result));
    
  } catch (error) {
    console.error('❌ 同步聊天失败:', error.message);
  }
}

testSimple();
