const { <PERSON><PERSON>beiP<PERSON>ider } = require('./dist');

async function testStreamChat() {
  console.log('🌊 测试流式聊天功能...');
  
  const provider = new DangbeiProvider({}, false);
  
  let messageCount = 0;
  let totalContent = '';
  let completed = false;
  
  const timeout = setTimeout(() => {
    if (!completed) {
      console.log('\n⏰ 超时结束，当前状态:');
      console.log('📊 收到', messageCount, '个消息片段');
      console.log('📝 内容:', JSON.stringify(totalContent));
      process.exit(0);
    }
  }, 10000);
  
  try {
    console.log('🚀 开始发送聊天请求...');

    const promise = provider.chat({
      conversationId: 'stream-test-' + Date.now(),
      question: '你好'
    }, {
      onMessage: (content, data) => {
        messageCount++;
        totalContent += content;
        console.log('📨 片段', messageCount + ':', JSON.stringify(content));
      },
      onComplete: (data) => {
        completed = true;
        clearTimeout(timeout);
        console.log('✅ 聊天完成！总共', messageCount, '个片段');
        console.log('📝 完整内容:', JSON.stringify(totalContent));
        console.log('📋 对话ID:', data.conversation_id);
      },
      onError: (error) => {
        completed = true;
        clearTimeout(timeout);
        console.error('❌ 聊天错误:', error.message);
        console.error('❌ 错误详情:', error);
      }
    });

    console.log('⏳ 等待聊天响应...');
    await promise;
    console.log('🏁 聊天Promise已完成');

  } catch (error) {
    completed = true;
    clearTimeout(timeout);
    console.error('❌ 流式聊天测试失败:', error.message);
    console.error('❌ 错误详情:', error);
  }
}

testStreamChat();
