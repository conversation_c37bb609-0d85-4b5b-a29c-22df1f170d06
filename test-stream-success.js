const { <PERSON><PERSON><PERSON><PERSON><PERSON>ider } = require('./dist');

async function testStreamingChat() {
  console.log('🌊 当贝AI流式聊天功能测试');
  console.log('================================');
  
  const provider = new DangbeiProvider({}, false);
  
  let messageCount = 0;
  let totalContent = '';
  let startTime = Date.now();
  
  try {
    console.log('🚀 开始流式聊天...');
    console.log('💬 问题: "请简单介绍一下人工智能"');
    console.log('📡 实时响应:');
    
    await provider.chat({
      conversationId: 'stream-demo-' + Date.now(),
      question: '请简单介绍一下人工智能'
    }, {
      onMessage: (content, data) => {
        messageCount++;
        totalContent += content;
        // 实时显示内容，不换行
        process.stdout.write(content);
      },
      onComplete: (data) => {
        const duration = Date.now() - startTime;
        console.log('\n\n✅ 聊天完成！');
        console.log('================================');
        console.log(`📊 统计信息:`);
        console.log(`   - 消息片段数: ${messageCount}`);
        console.log(`   - 总字符数: ${totalContent.length}`);
        console.log(`   - 响应时间: ${duration}ms`);
        console.log(`   - 对话ID: ${data.conversation_id || '未提供'}`);
        console.log(`📝 完整内容: "${totalContent}"`);
      },
      onError: (error) => {
        console.error('\n❌ 聊天错误:', error.message);
      }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 同时测试同步聊天
async function testSyncChat() {
  console.log('\n🔄 同步聊天功能测试');
  console.log('================================');
  
  const provider = new DangbeiProvider({}, false);
  
  try {
    const startTime = Date.now();
    const result = await provider.chatSync({
      conversationId: 'sync-demo-' + Date.now(),
      question: '你好'
    });
    const duration = Date.now() - startTime;
    
    console.log('✅ 同步聊天完成！');
    console.log(`📝 响应内容: "${result}"`);
    console.log(`⏱️ 响应时间: ${duration}ms`);
    
  } catch (error) {
    console.error('❌ 同步聊天失败:', error.message);
  }
}

async function runTests() {
  await testStreamingChat();
  await testSyncChat();
  
  console.log('\n🎉 所有测试完成！');
  console.log('✅ 流式响应功能已修复并正常工作');
}

runTests();
