const { <PERSON><PERSON><PERSON>P<PERSON>ider } = require('./dist');

async function testStreamChatFinal() {
  console.log('🌊 最终流式聊天测试...');
  
  const provider = new DangbeiProvider({}, false);
  
  let messageCount = 0;
  let totalContent = '';
  
  try {
    console.log('🚀 开始流式聊天...');
    
    await provider.chat({
      conversationId: 'stream-test-' + Date.now(),
      question: '你好'
    }, {
      onMessage: (content, data) => {
        console.log('🎉 onMessage被调用了！');
        messageCount++;
        totalContent += content;
        console.log(`📨 收到消息片段 ${messageCount}: "${content}"`);
      },
      onComplete: (data) => {
        console.log('🎉 onComplete被调用了！');
        console.log('✅ 聊天完成！');
        console.log(`📊 总共收到 ${messageCount} 个消息片段`);
        console.log(`📝 完整内容: "${totalContent}"`);
        console.log(`📋 对话ID: ${data.conversation_id}`);
      },
      onError: (error) => {
        console.log('🎉 onError被调用了！');
        console.error('❌ 聊天错误:', error.message);
      }
    });
    
    console.log('🏁 流式聊天完成');
    
  } catch (error) {
    console.error('❌ 流式聊天测试失败:', error.message);
  }
}

testStreamChatFinal();
